# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build & Development
- `npm run dev` - Run both client and server in development mode with live reloading
- `npm run start:client` - Run webpack dev server for client only
- `npm run start:server-dev` - Run server only with development settings
- `npm run build-dev` - Build client in development mode
- `npm run build-prod` - Build client for production
- `npm run tunnel` - Build production and start server (for tunneling)

### Testing & Quality Assurance
- `npm test` - Run Jest test suite
- `npm run test:coverage` - Run tests with coverage reporting
- `npm run perf` - Run performance tests in tests/perf/
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Run ESLint with auto-fix
- `npm run format` - Format code with Prettier

### Important Testing Requirements
- All code changes in `src/core` **MUST** be tested per contribution guidelines
- Coverage thresholds are enforced: 21.5% statements, 17% branches, 22% lines, 20.5% functions
- Use `npm run test:coverage` to verify coverage before submitting changes

## Project Architecture

### High-Level Structure
OpenFront.io is a real-time strategy game with a client-server architecture:

- **`src/client/`** - Frontend game client (GPL v3 license)
- **`src/core/`** - Shared game logic between client and server (MIT license)  
- **`src/server/`** - Backend game server (MIT license)
- **`resources/`** - Static assets (images, maps, flags, etc.)
- **`tests/`** - Test files mirroring src structure

### Core Technologies
- **TypeScript** - Primary language with strict mode enabled
- **Webpack** - Module bundling and development server
- **PixiJS** - Client-side 2D graphics rendering
- **WebSockets** - Real-time client-server communication
- **Jest** - Testing framework with ts-jest
- **ESLint + Prettier** - Code quality and formatting
- **Tailwind CSS** - Styling framework

### Key Architectural Patterns

#### Game State Management
- `GameImpl` - Core game state container
- `GameRunner` - Handles game loop and updates
- `ExecutionManager` - Processes game actions/commands
- Shared validation logic between client and server in `src/core/`

#### Client Architecture
- **Rendering**: PixiJS-based graphics with layered rendering system
- **UI Components**: Custom web components using Lit framework
- **Input Handling**: Centralized input processing with radial menus
- **Transport Layer**: WebSocket communication with message schemas

#### Server Architecture  
- **Master/Worker Pattern**: Main server spawns worker processes for game instances
- **Game Management**: `GameManager` handles lobbies and game lifecycle
- **Authentication**: JWT-based auth with Discord integration
- **Rate Limiting**: Express-based API with rate limiting

#### Shared Core Systems
- **Pathfinding**: A* algorithm implementations for unit movement
- **Execution System**: Command pattern for all game actions
- **Validation**: Zod schemas for API and message validation
- **Configuration**: Environment-specific configs with type safety

### Development Environment
- Development server runs on port 9000 (client) and 3000 (server)
- Webpack dev server proxies API requests and WebSocket connections
- Hot module replacement enabled for client development
- ESM modules with TypeScript compilation

### Testing Strategy
- Unit tests for core game logic
- Integration tests for client-server communication  
- Performance tests for pathfinding and game updates
- Visual regression testing for UI components
- All tests use Jest with TypeScript support

### Build Process
- Webpack bundles client code with content hashing
- Separate vendor chunk splitting for better caching
- Production builds include minification and optimization
- Static assets copied from resources/ to static/

### Multi-licensing
- Server and core code: MIT License
- Client code: GPL v3 License  
- Non-commercial assets: CC BY-NC 4.0 with commercial exemption

### Performance Considerations
- Game loop runs at 60 FPS with optimized update cycles
- WebWorkers for computationally intensive tasks
- Efficient pathfinding with multiple A* implementations
- Asset optimization and lazy loading strategies