@tailwind base;
@tailwind components;
@tailwind utilities;
@import url("./styles/core/flag-animation.css");
@import url("./styles/core/variables.css");
@import url("./styles/core/typography.css");
@import url("./styles/layout/header.css");
@import url("./styles/layout/footer.css");
@import url("./styles/layout/container.css");
@import url("./styles/components/button.css");
@import url("./styles/components/modal.css");
@import url("./styles/modal/chat.css");
@import url("./styles/components/setting.css");
@import url("./styles/components/controls.css");
* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

/* Add custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}
.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}

.start-game-button {
  width: 100%;
  max-width: 300px;
  padding: 15px 20px;
  font-size: 16px;
  cursor: pointer;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  transition: background-color 0.3s;
  display: inline-block;
  margin: 0 0 20px 0;
}

.start-game-button:not(:disabled):hover {
  background-color: #0056b3;
}

.start-game-button:disabled {
  background: linear-gradient(to right, #4a4a4a, #3d3d3d);
  opacity: 0.7;
  cursor: not-allowed;
}

.options-layout {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
  margin: 24px 0;
}

.options-section {
  background: rgba(0, 0, 0, 0.2);
  padding: 12px 24px 24px 24px;
  border-radius: 12px;
}

.option-title {
  margin: 0 0 16px 0;
  font-size: 20px;
  color: #fff;
  text-align: center;
}

.option-cards {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  gap: 16px;
}

.option-card {
  width: 100%;
  min-width: 100px;
  max-width: 120px;
  padding: 4px 4px 0 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  background: rgba(30, 30, 30, 0.95);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.option-card:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(40, 40, 40, 0.95);
}

.option-card.selected {
  border-color: #4a9eff;
  background: rgba(74, 158, 255, 0.1);
}

.option-card-title {
  font-size: 14px;
  color: #aaa;
  text-align: center;
  margin: 0 0 4px 0;
}

.option-image {
  width: 100%;
  aspect-ratio: 4/2;
  color: #aaa;
  transition: transform 0.2s ease-in-out;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.option-card input[type="checkbox"] {
  display: none;
}

label.option-card:hover {
  transform: none;
}

.checkbox-icon {
  width: 16px;
  height: 16px;
  border: 2px solid #aaa;
  border-radius: 6px;
  margin: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;
}

.option-card.selected .checkbox-icon {
  border-color: #4a9eff;
  background: #4a9eff;
}

.option-card.selected .checkbox-icon::after {
  content: "✓";
  color: white;
}

/* HostLobbyModal css */
.clipboard-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.visibility-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.copy-success {
  position: relative;
  color: green;
  font-size: 14px;
  margin-top: 5px;
}

.copy-success-icon {
  width: 18px;
  height: 18px;
  color: #4caf50;
}

.lobby-id-box {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin: 8px 0px 0px 0px;
}

#lobbyIdInput {
  font-family: monospace;
  font-weight: 600;
}

.lobby-id-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(0, 0, 0, 0.2);
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.lobby-id-button:hover {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.lobby-id {
  font-size: 14px;
  color: #fff;
  text-align: center;
  font-family: monospace;
  font-weight: 600;
}

.players-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  padding: 0 16px;
}

.player-tag {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: #2a2a2a;
  color: #fff;
  padding: 8px 12px;
  margin: 4px;
  border-radius: 16px;
  font-size: 14px;
}

#bots-count,
#private-lobby-bots-count {
  width: 80%;
  height: 16px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Firefox */
#bots-count::-moz-range-track,
#private-lobby-bots-count::-moz-range-track {
  height: 8px;
  background: white;
}

#bots-count::-moz-range-progress,
#private-lobby-bots-count::-moz-range-progress {
  height: 8px;
  background-color: #0075ff;
}

#bots-count::-moz-range-thumb,
#private-lobby-bots-count::-moz-range-thumb {
  height: 16px;
  width: 16px;
  background: #0075ff;
  border: none;
  border-radius: 50%;
}

/* Chrome */
#bots-count::-webkit-slider-runnable-track,
#private-lobby-bots-count::-webkit-slider-runnable-track {
  height: 8px;
  background: linear-gradient(
    to right,
    #0075ff var(--progress, 0%),
    white var(--progress, 0%)
  );
}

#bots-count::-webkit-slider-thumb,
#private-lobby-bots-count::-webkit-slider-thumb {
  -webkit-appearance: none;
  height: 16px;
  width: 16px;
  background: #0075ff;
  border: none;
  border-radius: 50%;
  margin-top: -4px;
}

.random-map {
  border: 2px solid rgba(255, 255, 255, 0.1);
  background: rgba(30, 30, 30, 0.95);
}

.random-map.selected {
  border: 2px solid #4a9eff;
  background: rgba(74, 158, 255, 0.1);
}

#helpModal table {
  border-collapse: collapse;
}

#helpModal table,
#helpModal table th,
#helpModal table td {
  border: 1px solid rgb(255 255 255 / 0.2);
}

#helpModal table th,
#helpModal table td {
  padding: 8px 16px;
}

#helpModal table td:first-of-type {
  text-align: center;
}

#helpModal .default-image {
  width: 12rem;
  max-width: 12rem;
  height: auto;
  object-fit: contain;
  display: block;
}

#helpModal .info-panel-img {
  width: 12rem;
  max-width: 12rem;
  height: auto;
  object-fit: contain;
  display: block;
}

#helpModal .player-icon-img {
  width: 14rem;
  height: 14rem;
  object-fit: contain;
}

#helpModal .icon {
  background-color: white;
  width: 32px;
  height: 32px;
}

#helpModal .city-icon {
  mask: url("../../resources/images/CityIconWhite.svg") no-repeat center / cover;
}

#helpModal .factory-icon {
  mask: url("../../resources/images/FactoryIconWhite.svg") no-repeat center /
    cover;
}

#helpModal .defense-post-icon {
  mask: url("../../resources/images/ShieldIconWhite.svg") no-repeat center /
    cover;
}

#helpModal .port-icon {
  mask: url("../../resources/images/PortIcon.svg") no-repeat center / cover;
}

#helpModal .warship-icon {
  mask: url("../../resources/images/BattleshipIconWhite.svg") no-repeat center /
    cover;
}

#helpModal .missile-silo-icon {
  mask: url("../../resources/non-commercial/svg/MissileSiloIconWhite.svg")
    no-repeat center / cover;
}

#helpModal .sam-launcher-icon {
  mask: url("../../resources/non-commercial/svg/SamLauncherIconWhite.svg")
    no-repeat center / cover;
}

#helpModal .atom-bomb-icon {
  mask: url("../../resources/images/NukeIconWhite.svg") no-repeat center / cover;
}

#helpModal .hydrogen-bomb-icon {
  mask: url("../../resources/images/MushroomCloudIconWhite.svg") no-repeat
    center / cover;
}

#helpModal .mirv-icon {
  mask: url("../../resources/images/MIRVIcon.svg") no-repeat center / cover;
}

#helpModal .chat-icon {
  mask: url("../../resources/images/ChatIconWhite.svg") no-repeat center / cover;
}

#helpModal .target-icon {
  mask: url("../../resources/images/TargetIcon.svg") no-repeat center / cover;
}

#helpModal .alliance-icon {
  mask: url("../../resources/images/AllianceIconWhite.svg") no-repeat center /
    cover;
}

#helpModal .emoji-icon {
  mask: url("../../resources/images/EmojiIconWhite.svg") no-repeat center /
    cover;
}

#helpModal .betray-icon {
  mask: url("../../resources/images/TraitorIconWhite.svg") no-repeat center /
    cover;
}

#helpModal .donate-icon {
  mask: url("../../resources/images/DonateTroopIconWhite.svg") no-repeat
    center / cover;
}

#helpModal .donate-gold-icon {
  mask: url("../../resources/images/DonateGoldIconWhite.svg") no-repeat center /
    cover;
}

#helpModal .build-icon {
  mask: url("../../resources/images/BuildIconWhite.svg") no-repeat center /
    cover;
}

#helpModal .info-icon {
  mask: url("../../resources/images/InfoIcon.svg") no-repeat center / cover;
}

#helpModal .boat-icon {
  mask: url("../../resources/images/BoatIcon.svg") no-repeat center / cover;
}

#helpModal .cancel-icon {
  mask: url("../../resources/images/XIcon.svg") no-repeat center / cover;
}

@media screen and (max-width: 768px) {
  #helpModal .modal-content {
    max-height: 90vh;
    max-width: 100vw;
    width: 100%;
  }
  #error-modal {
    max-width: 575px;
  }
}

@media screen and (max-width: 480px) {
  #error-modal {
    max-width: 350px;
  }
}

#error-modal {
  position: fixed;
  padding: 20px;
  background: white;
  border: 1px solid black;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  width: 87%;
  box-sizing: border-box;
}

#error-modal pre {
  overflow-x: auto;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 400px;
}

#error-modal button.copy-btn {
  padding: 8px 16px;
  margin-top: 10px;
  background: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

#error-modal button.close-btn {
  color: white;
  top: 0px;
  right: 0px;
  cursor: pointer;
  background: red;
  margin-right: 0px;
  position: fixed;
  width: 40px;
}

.start-game-button-container {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: 20px;
}

.message-area {
  margin-top: 10px;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
  transition: opacity 0.3s ease;
  opacity: 0;
  height: 0;
  overflow: hidden;
}

.message-area.show {
  opacity: 1;
  height: auto;
  margin-bottom: 10px;
}

.message-area.error {
  background-color: #ffebee;
  color: #c62828;
}

.message-area.success {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.lobby-id-box {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin: 40px 0px 0px 0px;
}

.lobby-id-box input {
  flex-grow: 1;
  max-width: 200px;
  outline: 0;
  padding: 10px;
  font-size: 16px;
  border: 1px solid #ccc;
  color: #202020;
  border-radius: 8px;
}

.lobby-id-paste-button {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.2);
  padding: 10px 16px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.lobby-id-paste-button:hover {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.lobby-id-paste-button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.options-section {
  background: rgba(0, 0, 0, 0.2);
  padding: 12px 24px 24px 24px;
  border-radius: 12px;
}

.option-title {
  margin: 0 0 16px 0;
  font-size: 20px;
  color: #fff;
  text-align: center;
}

.players-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  padding: 0 16px;
}

/* News Button Notification */
news-button .active button {
  position: relative;
  border-color: #2563eb !important;
  border-width: 2px !important;
  box-shadow:
    0 0 0 1px rgba(37, 99, 235, 0.5),
    0 0 8px rgba(37, 99, 235, 0.4);
}

news-button .active button::after {
  content: "";
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  background: #f59e0b;
  border: 2px solid white;
  border-radius: 50%;
  animation: newsPulse 1.5s ease-in-out infinite;
}

@keyframes newsPulse {
  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.4);
  }
}

.host-badge {
  font-size: 11px;
  color: #4caf50;
  font-weight: bold;
}

.remove-player-btn {
  width: 16px;
  height: 16px;
  border: none;
  background: #ff4444;
  color: white;
  border-radius: 50%;
  font-size: 11px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
}

.remove-player-btn:hover {
  background: #ff6666;
}
