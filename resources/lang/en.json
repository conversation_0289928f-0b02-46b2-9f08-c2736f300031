{"lang": {"en": "English", "native": "English", "svg": "uk_us_flag", "lang_code": "en"}, "common": {"close": "Close"}, "main": {"title": "OpenFront (ALPHA)", "join_discord": "Join the Discord!", "login_discord": "Login with Discord", "checking_login": "Checking login...", "logged_in": "Logged in!", "log_out": "Log out", "create_lobby": "Create Lobby", "join_lobby": "Join <PERSON><PERSON>", "single_player": "Single Player", "instructions": "Instructions", "how_to_play": "How to Play", "advertise": "Advertise", "wiki": "Wiki", "privacy_policy": "Privacy Policy", "terms_of_service": "Terms of Service"}, "news": {"see_all_releases": "See all releases", "github_link": "on GitHub", "title": "Release Notes"}, "help_modal": {"hotkeys": "Hotkeys", "table_key": "Key", "table_action": "Action", "action_alt_view": "Alternate view (terrain/countries)", "action_attack_altclick": "Attack (when left click is set to open menu)", "action_build": "Open build menu", "action_emote": "Open emote menu", "action_center": "Center camera on player", "action_zoom": "Zoom out/in", "action_move_camera": "Move camera", "action_ratio_change": "Decrease/Increase attack ratio", "action_reset_gfx": "Reset graphics", "ui_section": "Game UI", "ui_leaderboard": "Leaderboard", "ui_your_team": "Your team:", "ui_leaderboard_desc": "Shows the top players of the game and their names, % owned land, gold and troops. Using Show All shows all players in the game. If you don't want to see the leaderboard, click Hide.", "ui_control": "Control panel", "ui_control_desc": "The control panel contains the following elements:", "ui_pop": "Pop - The amount of units you have, your max population and the rate at which you gain them.", "ui_gold": "Gold - The amount of gold you have and the rate at which you gain it.", "ui_attack_ratio": "Attack ratio - The amount of troops that will be used when you attack. You can adjust the attack ratio using the slider. Having more attacking troops than defending troops will make you lose fewer troops in the attack, while having less will increase the damage dealt to your attacking troops. The effect doesn't go beyond ratios of 2:1.", "ui_events": "Event panel", "ui_events_desc": "The Event panel displays the latest events, requests and Quick Chat messages. Some examples are:", "ui_events_alliance": "Alliance - Alliance requests can be accepted or rejected. Allies can share resources and troops, but can't attack each other. Clicking Focus moves the view to the player who sent the request.", "ui_events_attack": "Attacks - Incoming attacks and your outgoing attacks are shown. Click the message to center the view on the attack, nuke or Boat (transport ship). You can retreat troops by clicking the red X button. This will cost the lives of 25% of your attacking troops. If you retrieve a Boat attack, the boat returns to its starting point and will attack there if the land has been captured since. Nukes can't be retreated once launched.", "ui_events_quickchat": "Quick Chat - You can see sent and recieved chat messages here. Send a message to a player by clicking the Quick Chat icon in their Info menu.", "ui_options": "Options", "ui_options_desc": "The following elements can be found inside:", "ui_playeroverlay": "Player info overlay", "ui_playeroverlay_desc": "When you hover over a country, the Player info overlay is displayed under Options. It shows the type of player: Human, Nation (smart bot), or Bot. A Nation's attitude towards you, ranging from Hostile to Friendly. And defending troops, gold, plus the number of Warships and various buildings the player has.", "ui_wilderness": "Wilderness", "option_pause": "Pause/Unpause the game - Only available in single player mode.", "option_timer": "Timer - Time passed since the start of the game.", "option_exit": "Exit button.", "option_settings": "Settings - Open the settings menu. Inside you can toggle the Alternate view, Emojis, Dark Mode, <PERSON> (anonymous/random names mode), and action on left click.", "radial_title": "Radial menu", "radial_desc": "Right clicking (or touch on mobile) opens the Radial menu. Right click outside it to close it. From the menu you can:", "radial_build": "Open the Build menu.", "radial_attack": "Open the Attack menu.", "radial_info": "Open the Info menu.", "radial_boat": "Send a Boat (transport ship) to attack at the selected location. Only available if you have access to water.", "radial_close": "Close the menu.", "info_title": "Info menu", "info_enemy_desc": "Contains information such as the selected player's name, gold, troops, stopped trading with you, nukes sent to you, and if the player is a traitor. Stopped trading means you won't receive gold from them and they won't sent you gold via trade ships. Manually (if the player clicked \"Stop trading\", which lasts until you both click \"Start trading\") or automatically (if you betrayed your alliance, which lasts until you become allies again or after 5 minutes). Traitor displays Yes for 30 seconds when the player betrayed and attacked a player who was in an alliance with them. The icons below represent the following interactions:", "info_chat": "Send a quick chat message to the player. Select a Category, a Phrase, and if the phrase contains [P1] select a Player name to replace it with. Hit Send.", "info_target": "Place a target mark on the player, marking it for all allies, used to coordinate attacks.", "info_alliance": "Send an alliance request to the player. Allies can share resources and troops, but can't attack each other.", "info_emoji": "Send an emoji to the player.", "info_trade": "Use \"Stop trading\" to stop giving the player gold and receiving their gold via trade ships. If you both click \"Start trading\" it will start again.", "info_ally_panel": "Ally info panel", "info_ally_desc": "When you ally with a player, the following new icons become available:", "ally_betray": "Betray your ally, ending the alliance, halting trade, and weakening your defense. Trading between you is paused for 5 minutes (or until you become allies again) and others may stop trading too. And unless the other player was a traitor themselves, you'll be marked a traitor for 30 seconds. During this time an icon will be above your name and you will have a 50% defense debuff. Bots are less likely to ally with you and players will think twice before doing so.", "ally_donate": "Donate some of your troops to your ally. Used when they're low on troops and are being attacked, or when they need that extra power to crush an enemy.", "ally_donate_gold": "Donate some of your gold to your ally. Used when they're low on gold and need it for buildings, or when your team member is saving for that MIRV.", "build_menu_title": "Build menu", "build_menu_desc": "Build these or see how many of each you already build:", "build_name": "Name", "build_icon": "Icon", "build_desc": "Description", "build_city": "City", "build_city_desc": "Increases your max population. Useful when you can't expand your territory or you're about to hit your population limit.", "build_factory": "Factory", "build_factory_desc": "Creates railroads automatically with nearby structures, and spawns trains sporadically.", "build_defense": "Defense Post", "build_defense_desc": "Increases defenses around nearby borders, which show a checkered pattern. Attacks from enemies are slower and have more casualties.", "build_port": "Port", "build_port_desc": "Can only be built near water. Allows building Warships. Automatically sends trade ships between ports of your country and other countries (except when trade is stopped), giving gold to both sides. Trade stops automatically when you attack or are attacked by a player. It resumes after 5 minutes or if you become allies. You can manually toggle trading with \"Stop trading\" or \"Start trading\".", "build_warship": "Warship", "build_warship_desc": "Patrols in an area, capturing enemy trade ships and destroying their Boats (transport ships) and Warships. Spawns from the nearest Port and patrols the area you first clicked to build it. You can control Warships by attack-clicking on them (see action Attack under Hotkeys) and then attack-clicking the new area you want them to move to.", "build_silo": "Missile Silo", "build_silo_desc": "Allows launching missiles.", "build_sam": "SAM Launcher", "build_sam_desc": "Can intercept enemy missiles in its 100 pixel range. With a 100% hit chance for Atom Bomb, 80% for Hydrogen Bomb and 50% for individual MIRV Warheads. The SAM has a 7.5 second cooldown.", "build_atom": "Atom Bomb", "build_atom_desc": "Small explosive bomb that destroys territory, buildings, ships and boats. Spawns from the nearest Missile Silo and lands in the area you first clicked to build it.", "build_hydrogen": "Hydrogen Bomb", "build_hydrogen_desc": "Large explosive bomb. Spawns from the nearest Missile Silo and lands in the area you first clicked to build it.", "build_mirv": "MIRV", "build_mirv_desc": "The most powerful bomb in the game. Splits up into smaller bombs that will cover a huge range of territory. Only damages the player that you first clicked on to build it. Spawns from the nearest Missile Silo and lands in the area you first clicked to build it.", "player_icons": "Player icons", "icon_desc": "Examples of some of the ingame icons you will encounter and what they mean:", "icon_crown": "Crown - Number 1. This is the top player in the leaderboard.", "icon_traitor": "Broken shield - Traitor. This player attacked an ally.", "icon_ally": "Handshake - Ally. This player is your ally.", "icon_embargo": "Dollar stop sign - Embargo. This player has stopped trading with you automatically or manually.", "icon_request": "Envelope - Alliance request. This player has sent you an alliance request.", "info_enemy_panel": "Enemy info panel", "exit_confirmation": "Are you sure you want to exit the game?"}, "single_modal": {"title": "Single Player", "allow_alliances": "Allow alliances", "options_title": "Options", "bots": "Bots: ", "bots_disabled": "Disabled", "disable_nations": "Disable Nations", "instant_build": "Instant build", "infinite_gold": "Infinite gold", "infinite_troops": "Infinite troops", "disable_nukes": "Disable Nukes", "enables_title": "Enable Settings", "start": "Start Game"}, "map": {"map": "Map", "world": "World", "giantworldmap": "Giant World Map", "europe": "Europe", "mena": "MENA", "northamerica": "North America", "oceania": "Oceania", "blacksea": "Black Sea", "africa": "Africa", "asia": "Asia", "mars": "Mars", "southamerica": "South America", "britannia": "Britannia", "gatewaytotheatlantic": "Gateway to the Atlantic", "australia": "Australia", "random": "Random", "iceland": "Iceland", "pangaea": "Pangaea", "eastasia": "East Asia", "betweentwoseas": "Between Two Seas", "faroeislands": "Faroe Islands", "deglaciatedantarctica": "Deglaciated Antarctica", "europeclassic": "Europe (classic)", "falklandislands": "Falkland Islands", "baikal": "Baikal", "halkidiki": "Halkidi<PERSON>", "straitofgibraltar": "Strait of Gibraltar", "italia": "Italia", "yenisei": "<PERSON><PERSON><PERSON>", "pluto": "Pluto"}, "map_categories": {"continental": "Continental", "regional": "Regional", "fantasy": "Other"}, "map_component": {"loading": "Loading..."}, "private_lobby": {"title": "Join <PERSON> Lobby", "enter_id": "Enter Lobby ID", "player": "Player", "players": "Players", "join_lobby": "Join <PERSON><PERSON>", "checking": "Checking lobby...", "not_found": "Lobby not found. Please check the ID and try again.", "error": "An error occurred. Please try again.", "joined_waiting": "Joined successfully! Waiting for game to start..."}, "public_lobby": {"join": "Join next Game", "waiting": "players waiting", "teams_Duos": "Duos (teams of 2)", "teams_Trios": "Trios (teams of 3)", "teams_Quads": "Quads (teams of 4)", "teams": "{num} teams"}, "username": {"enter_username": "Enter your username", "not_string": "Username must be a string.", "too_short": "Username must be at least {min} characters long.", "too_long": "Username must not exceed {max} characters.", "invalid_chars": "Username can only contain letters, numbers, spaces, underscores, and [square brackets]."}, "host_modal": {"title": "Private Lobby", "mode": "Mode", "team_count": "Number of Teams", "options_title": "Options", "bots": "Bots: ", "bots_disabled": "Disabled", "disable_nations": "Disable Nations", "instant_build": "Instant build", "infinite_gold": "Infinite gold", "infinite_troops": "Infinite troops", "enables_title": "Enable Settings", "player": "Player", "players": "Players", "waiting": "Waiting for players...", "start": "Start Game", "host_badge": "Host"}, "team_colors": {"red": "Red", "blue": "Blue", "teal": "<PERSON><PERSON>", "purple": "Purple", "yellow": "Yellow", "orange": "Orange", "green": "Green", "bot": "Bot"}, "game_starting_modal": {"title": "Game is Starting...", "desc": "Preparing for the lobby to start. Please wait."}, "difficulty": {"difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Relaxed": "Relaxed", "Balanced": "Balanced", "Intense": "Intense", "Impossible": "Impossible"}, "game_mode": {"ffa": "Free for All", "teams": "Teams"}, "select_lang": {"title": "Select Language"}, "unit_type": {"city": "City", "defense_post": "Defense Post", "port": "Port", "warship": "Warship", "missile_silo": "Missile Silo", "sam_launcher": "SAM Launcher", "atom_bomb": "Atom Bomb", "hydrogen_bomb": "Hydrogen Bomb", "mirv": "MIRV", "factory": "Factory"}, "user_setting": {"title": "User Settings", "tab_basic": "Basic Settings", "tab_keybinds": "Keybinds", "dark_mode_label": "Dark Mode", "dark_mode_desc": "Toggle the site’s appearance between light and dark themes", "dark_mode_enabled": "Dark mode enabled", "light_mode_enabled": "Light mode enabled", "emojis_label": "Emojis", "emojis_visible": "Emojis are visible", "emojis_hidden": "Emojis are hidden", "emojis_desc": "Toggle whether emojis are shown in game", "alert_frame_label": "<PERSON><PERSON>", "alert_frame_desc": "Toggle the alert frame. When enabled, the frame will be displayed when you are betrayed.", "special_effects_label": "Special effects", "special_effects_desc": "Toggle special effects. Deactivate to improve performances", "special_effects_enabled": "Special effects enabled", "special_effects_disabled": "Special effects disabled", "anonymous_names_label": "Hidden Names", "anonymous_names_desc": "Hide real player names with random ones on your screen.", "anonymous_names_enabled": "Anonymous names enabled", "lobby_id_visibility_label": "Hidden Lobby IDs", "lobby_id_visibility_desc": "Hide Lobby ID in private lobby creation", "real_names_shown": "Real names shown", "left_click_label": "Left Click to Open Menu", "left_click_desc": "When ON, left-click opens menu and sword button attacks. When OFF, left-click attacks directly.", "left_click_menu": "Left <PERSON>lick <PERSON>u", "left_click_opens_menu": "Left click opens menu", "right_click_opens_menu": "Right click opens menu", "attack_ratio_label": "⚔️ Attack Ratio", "attack_ratio_desc": "What percentage of your troops to send in an attack (1–100%)", "troop_ratio_desc": "Adjust the balance between troops (for combat) and workers (for gold production) (1–100%)", "territory_patterns_label": "🏳️ Territory Patterns", "territory_patterns_desc": "Choose whether to display territory pattern designs in game", "performance_overlay_label": "Performance Overlay", "performance_overlay_desc": "Toggle the performance overlay. When enabled, the performance overlay will be displayed. Press shift-D during game to toggle.", "easter_writing_speed_label": "Writing Speed Multiplier", "easter_writing_speed_desc": "Adjust how fast you pretend to code (x1–x100)", "easter_bug_count_label": "Bug Count", "easter_bug_count_desc": "How many bugs you're okay with (0–1000, emotionally)", "view_options": "View Options", "toggle_view": "Toggle View", "toggle_view_desc": "Alternate view (terrain/countries)", "attack_ratio_controls": "Attack Ratio Controls", "attack_ratio_up": "Increase Attack Ratio", "attack_ratio_up_desc": "Increase attack ratio by 10%", "attack_ratio_down": "Decrease Attack Ratio", "attack_ratio_down_desc": "Decrease attack ratio by 10%", "attack_keybinds": "Attack Keybinds", "boat_attack": "Boat Attack", "boat_attack_desc": "Send a boat attack to the tile under your cursor.", "ground_attack": "Ground Attack", "ground_attack_desc": "Send a ground attack to the tile under your cursor.", "zoom_controls": "Zoom Controls", "zoom_out": "Zoom Out", "zoom_out_desc": "Zoom out the map", "zoom_in": "Zoom In", "zoom_in_desc": "Zoom in the map", "camera_movement": "Camera Movement", "center_camera": "Center Camera", "center_camera_desc": "Center camera on player", "move_up": "Move Camera Up", "move_up_desc": "Move the camera upward", "move_left": "Move Camera Left", "move_left_desc": "Move the camera to the left", "move_down": "Move Camera Down", "move_down_desc": "Move the camera downward", "move_right": "Move Camera Right", "move_right_desc": "Move the camera to the right", "reset": "Reset", "unbind": "Unbind", "on": "On", "off": "Off", "toggle_terrain": "Toggle <PERSON>in", "terrain_enabled": "Terrain view enabled", "terrain_disabled": "Terrain view disabled", "exit_game_label": "Exit Game", "exit_game_info": "Return to main menu"}, "chat": {"title": "Quick Chat", "to": "Sent {user}: {msg}", "from": "From {user}: {msg}", "category": "Category", "phrase": "Phrase", "player": "Player", "send": "Send", "search": "Search player...", "build": "Build your message...", "cat": {"help": "Help", "attack": "Attack", "defend": "Defend", "greet": "Greetings", "misc": "Miscellaneous", "warnings": "Warnings"}, "help": {"troops": "Please give me troops!", "gold": "Please give me gold!", "no_attack": "Please don't attack me!", "sorry_attack": "Sorry, I didn’t mean to attack.", "alliance": "Alliance?", "help_defend": "Help me defend against [P1]!", "team_up": "Let’s team up against [P1]!"}, "attack": {"attack": "Attack [P1]!", "mirv": "Launch a MIRV at [P1]!", "focus": "Focus fire on [P1]!", "finish": "Let's finish off [P1]!"}, "defend": {"defend": "Defend [P1]!", "dont_attack": "Don’t attack [P1]!", "ally": "[P1] is my ally!"}, "greet": {"hello": "Hello!", "good_luck": "Good luck!", "have_fun": "Have fun!", "gg": "GG!", "nice_to_meet": "Nice to meet you!", "well_played": "Well played!", "hi_again": "Hi again!", "bye": "Bye!", "thanks": "Thanks!", "oops": "Oops, wrong button!", "trust_me": "You can trust me. Promise!", "trust_broken": "I trusted you..."}, "misc": {"go": "Let’s go!", "strategy": "Nice strategy!", "fun": "This game is fun!", "pr": "When will my PR finally get merged...?"}, "warnings": {"strong": "[P1] is strong.", "weak": "[P1] is weak.", "mirv_soon": "[P1] can launch a MIRV soon!", "number1_warning": "The #1 player will win soon unless we team up!", "stalemate": "Let's make peace. This is a stalemate, we will both lose.", "has_allies": "[P1] has many allies.", "no_allies": "[P1] has no allies.", "betrayed": "[P1] betrayed their ally!", "getting_big": "[P1] is growing too fast!", "danger_base": "[P1] is unprotected!", "saving_for_mirv": "[P1] is saving up to launch a MIRV.", "mirv_ready": "[P1] has enough gold to launch a MIRV!"}}, "build_menu": {"desc": {"atom_bomb": "Small explosion", "hydrogen_bomb": "Large explosion", "mirv": "Huge explosion, only targets selected player", "missile_silo": "Used to launch nukes", "sam_launcher": "Defends against incoming nukes", "warship": "Captures trade ships, destroys ships and boats", "port": "Sends trade ships to generate gold", "defense_post": "Increases defenses of nearby borders", "city": "Increases max population", "factory": "Creates railroads and spawns trains"}, "not_enough_money": "Not enough money"}, "win_modal": {"died": "You died", "your_team": "Your team won!", "other_team": "{team} team has won!", "you_won": "You Won!", "other_won": "{player} has won!", "exit": "Exit Game", "keep": "Keep Playing", "wishlist": "Wishlist on Steam!"}, "leaderboard": {"title": "Leaderboard", "hide": "<PERSON>de", "rank": "Rank", "player": "Player", "team": "Team", "owned": "Owned", "gold": "Gold", "troops": "Troops"}, "player_info_overlay": {"type": "Type", "bot": "Bot", "nation": "Nation", "player": "Player", "team": "Team", "d_troops": "Defending troops", "a_troops": "Attacking troops", "gold": "Gold", "ports": "Ports", "cities": "Cities", "factories": "Factories", "missile_launchers": "Missile launchers", "sams": "SAMs", "warships": "Warships", "health": "Health", "attitude": "Attitude", "levels": "Levels"}, "events_display": {"retreating": "retreating", "boat": "Boat", "alliance_request_status": "{name} {status} your alliance request", "alliance_accepted": "accepted", "alliance_rejected": "rejected", "duration_second": "1 second", "betrayal_description": "You broke your alliance with {name}, making you a TRAITOR ({malusPercent}% defense debuff for {durationText})", "duration_seconds_plural": "{seconds} seconds", "betrayed_you": "{name} broke their alliance with you", "about_to_expire": "Your alliance with {name} is about to expire!", "alliance_expired": "Your alliance with {name} expired", "attack_request": "{name} requests you attack {target}", "sent_emoji": "Sent {name}: {emoji}", "renew_alliance": "Request to renew", "request_alliance": "{name} requests an alliance!", "focus": "Focus", "accept_alliance": "Accept", "reject_alliance": "Reject", "alliance_renewed": "Your alliance with {name} has been renewed", "ignore": "Ignore"}, "unit_info_modal": {"structure_info": "Structure Info", "unit_type_unknown": "Unknown", "close": "Close", "cooldown": "Cooldown", "type": "Type", "upgrade": "Upgrade", "level": "Level"}, "relation": {"hostile": "Hostile", "distrustful": "Distrustful", "neutral": "Neutral", "friendly": "Friendly", "default": "<PERSON><PERSON><PERSON>"}, "control_panel": {"gold": "Gold", "troops": "Troops", "attack_ratio": "Attack Ratio"}, "player_panel": {"gold": "Gold", "troops": "Troops", "betrayals": "Number of betrayals", "traitor": "Traitor", "alliance_time_remaining": "Alliance Expires In", "embargo": "Stopped trading with you", "nuke": "Nukes sent by them to you", "start_trade": "Start trading", "stop_trade": "Stop trading", "yes": "Yes", "no": "No", "none": "None", "alliances": "Alliances"}, "replay_panel": {"replay_speed": "Replay speed", "game_speed": "Game speed", "fastest_game_speed": "max"}, "error_modal": {"crashed": "Game crashed!", "connection_error": "Connection error!", "paste_discord": "Please paste the following in your bug report in Discord:", "copy_clipboard": "Copy to clipboard", "copied": "Copied!", "failed_copy": "Failed to copy", "desync_notice": "You are desynced from other players. What you see might differ from other players."}, "heads_up_message": {"choose_spawn": "Choose a starting location"}, "territory_patterns": {"title": "Select Territory Pattern", "purchase": "Purchase", "blocked": {"login": "You must be logged in to access this pattern.", "purchase": "Purchase this pattern to unlock it."}, "pattern": {"default": "<PERSON><PERSON><PERSON>", "custom": "Custom", "stripes_v": "Vertical", "stripes_h": "Horizontal", "horizontal_stripes": "Horizontal (Alt)", "vertical_bars": "Vertical (Alt)", "checkerboard": "Checkerboard", "choco": "Choco", "diagonal": "Diagonal", "cross": "Cross", "mini_cross": "Mini Cross", "sword": "Sword", "sparse_dots": "Sparse Dots", "evan": "<PERSON>", "diagonal_stripe": "Diagonal Stripe", "mountain_ridge": "Mountain Ridge", "scattered_dots": "Scattered Dots", "circuit_board": "Circuit Board", "shells": "Shells", "-w-": ".w.", "white_rabbit": "White Rabbit", "goat": "Goa<PERSON>", "cats": "Cats", "cursor": "<PERSON><PERSON><PERSON>", "hand": "Hand", "radiation": "Radiation", "openfront_qr": "OpenFront.io QR code", "openfront": "OpenFront", "t_rex": "T-Rex", "embelem": "Emblem", "contributor": "Contributor", "grogu_head": "<PERSON><PERSON><PERSON> Head", "grogu": "<PERSON><PERSON><PERSON>"}}, "spawn_ad": {"loading": "Loading advertisement..."}, "auth": {"login_required": "<PERSON><PERSON> is required to access this website.", "redirecting": "You are being redirected...", "not_authorized": "You are not authorized to access this website.", "contact_admin": "If you believe you are seeing this message in error, please contact the website administrator."}}