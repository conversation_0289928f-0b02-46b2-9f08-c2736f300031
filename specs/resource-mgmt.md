# Product Requirements Document: Oil and Food Resource Allocation System

## Overview

This document outlines the requirements for adding an oil and food resource allocation system to the strategic war simulation game. The aim is to introduce a new layer of real-time resource management, enhancing player strategy and engagement without disrupting the existing core gameplay.

---

## Objective

- Add two new strategic resources (Oil and Food) that players must balance via sliders.
- Resource allocation directly impacts army effectiveness, mobility, and overall game outcomes.
- Integrate the mechanic seamlessly with existing UI and game logic.

---

## Background / Motivation

Currently, gold accumulates via trade, but military operations do not require active management of supply chains or consumable resources. By introducing Oil and Food as resources that must be allocated, players face meaningful trade-offs between rapid offensives, effective defenses, and long-term sustainability. This increases strategic depth and aligns with the game's war simulation theme.

---

## Core Features

### 1. Resources

- **Oil:** Fuels vehicles, warships, and possibly air units. Impacts movement speed and attack frequency.
- **Food:** Maintains soldier morale and combat effectiveness. Impacts unit defense, morale, and regeneration.

### 2. Resource Sliders

- Two sliders in the UI: Oil Allocation (%) and Food Allocation (%)
- The sum of Oil and Food allocation must not exceed 100%.
- Players can adjust these sliders in real time.
- As Oil or Food allocation increases, army benefits improve in that area, but at the expense of the other.

### 3. Effects of Allocation

- **High Oil / Low Food:**
  - Units move and attack faster, but morale and defense drop.
- **High Food / Low Oil:**
  - Units are resilient and strong in defense but move and attack less effectively.
- **Balanced:** 
  - Moderate performance in both offense and defense.

#### Example Effects Table

| Allocation      | Movement Speed | Attack Rate | Defense/Morale |
|-----------------|:-------------:|:-----------:|:--------------:|
| High Oil        |    +50%        |    +25%     |     -20%       |
| High Food       |    -20%        |    -15%     |     +50%       |
| Balanced (50/50)|    +15%        |    +5%      |     +10%       |

*Exact numbers to be refined during playtesting.*

### 4. Resource Consumption

- Oil and Food reserves decrease based on allocation and active army size.
- If either resource reaches critically low levels, corresponding penalties apply (e.g., movement halted, attrition damage, morale collapse).
- Reserves can be replenished through capturing supply points, territory, or trade.

### 5. UI/UX

- Sliders appear in the main resource panel, similar to the "attack %" setting.
- Real-time feedback: Adjusting sliders shows immediate projected effects (e.g., stat changes, warnings).
- Warnings and notifications if resources or allocations fall below safe thresholds.

### 6. Game Integration

- Army/unit stats are dynamically modified based on current allocation.
- AI-controlled factions use the mechanic for parity and challenge.
- Resource events (e.g., blockades, territory loss) dynamically impact available oil and food.

---

## Success Criteria

- Players regularly adjust sliders in response to tactical and strategic situations.
- Players experience meaningful consequences for poor resource management.
- Increased depth and replayability without overwhelming new players.

---

## Non-Goals

- No introduction of micro-management or highly granular resource logistics (e.g., per-unit supply lines).
- No changes to gold accumulation, which remains tied to trade.

---

## Open Questions

- How quickly should oil/food reserves deplete relative to army size and activity?
- What are the best default slider positions for new games?
- Should certain units (e.g., nuclear weapons, SAMs) have special resource requirements?

---

## Next Steps

1. Finalize effect formulas and depletion rates.
2. Design and approve UI mockups.
3. Implement core logic and integrate with main game loop.
4. Playtest for balance and adjust as needed.

---