// https://www.typescriptlang.org/tsconfig/
{
  "compilerOptions": {
    // Language and Environment
    "target": "ES2020",

    // Modules
    "module": "ESNext",
    "rootDir": ".",
    "moduleResolution": "node",

    // Emit
    "sourceMap": true,

    // Type Checking
    "allowSyntheticDefaultImports": true,
    "allowUnusedLabels": false,
    "alwaysStrict": true,
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "resolveJsonModule": true,
    "strictNullChecks": true,
    "useDefineForClassFields": false,
    "strictPropertyInitialization": false,
    "strict": true
  },
  "include": [
    "src/**/*",
    "resources/**/*",
    "generated/**/*",
    "tests/**/*",
    "src/scripts"
  ],
  "exclude": ["node_modules"]
}
