# SSH Configuration
SSH_KEY=~/.ssh/your-ssh-key

# Docker Configuration
DOCKER_USERNAME=username
DOCKER_REPO=your-repo-name
DOCKER_TOKEN=your_docker_token_here

# Admin credentials
ADMIN_TOKEN=your_admin_token_here

# Cloudflare Configuration
CF_ACCOUNT_ID=your_cloudflare_account_id
CF_API_TOKEN=your_cloudflare_api_token
DOMAIN=your-domain.com

# R2 Configuration
R2_ACCESS_KEY=your_r2_access_key
R2_SECRET_KEY=your_r2_secret_key
R2_BUCKET=your-bucket-name

# Server Hosts
SERVER_HOST_STAGING=123.456.78.90
SERVER_HOST_EU=123.456.78.91
SERVER_HOST_US=123.456.78.92

# Version
VERSION_TAG="latest"
