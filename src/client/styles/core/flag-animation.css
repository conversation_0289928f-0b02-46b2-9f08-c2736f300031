@keyframes rainbow {
  0% {
    background-color: #990033;
  }
  16% {
    background-color: #996600;
  }
  32% {
    background-color: #336600;
  }
  48% {
    background-color: #008080;
  }
  64% {
    background-color: #1c3f99;
  }
  80% {
    background-color: #5e0099;
  }
  100% {
    background-color: #990033;
  }
}

.flag-color-rainbow {
  animation: rainbow 7s infinite;
}

@keyframes brightRainbow {
  0% {
    background-color: #ff0000;
  } /* Red */
  16% {
    background-color: #ffa500;
  } /* Orange */
  32% {
    background-color: #ffff00;
  } /* Yellow */
  48% {
    background-color: #00ff00;
  } /* Green */
  64% {
    background-color: #00ffff;
  } /* Cyan */
  80% {
    background-color: #0000ff;
  } /* Blue */
  100% {
    background-color: #ff0000;
  } /* Back to red */
}

.flag-color-bright-rainbow {
  animation: brightRainbow 7s linear infinite;
}

@keyframes copperGlow {
  0%,
  100% {
    background-color: #b87333;
    filter: brightness(1);
  }
  50% {
    background-color: #cd7f32;
    filter: brightness(1.4);
  }
}

.flag-color-copper-glow {
  animation: copperGlow 3s ease-in-out infinite;
}

@keyframes silverGlow {
  0%,
  100% {
    background-color: #c0c0c0;
    filter: brightness(1);
  }
  50% {
    background-color: #e0e0e0;
    filter: brightness(1.5);
  }
}

.flag-color-silver-glow {
  animation: silverGlow 3s ease-in-out infinite;
}

@keyframes goldGlow {
  0%,
  100% {
    background-color: #ffd700;
    filter: brightness(1);
  }
  50% {
    background-color: #fff8dc;
    filter: brightness(1.6);
  }
}

.flag-color-gold-glow {
  animation: goldGlow 3s ease-in-out infinite;
}

@keyframes neonPulseGreen {
  0%,
  100% {
    background-color: #39ff14;
    box-shadow:
      0 0 4px #39ff14,
      0 0 8px #39ff14;
    filter: brightness(1);
    transform: scale(1);
    opacity: 1;
  }
  25% {
    background-color: #2aff60;
    box-shadow:
      0 0 8px #2aff60,
      0 0 12px #2aff60;
    filter: brightness(1.2);
    transform: scale(1.05);
    opacity: 0.9;
  }
  50% {
    background-color: #00ff88;
    box-shadow:
      0 0 12px #00ff88,
      0 0 20px #00ff88;
    filter: brightness(1.4);
    transform: scale(1.12);
    opacity: 0.7;
  }
  75% {
    background-color: #2aff60;
    box-shadow:
      0 0 8px #2aff60,
      0 0 12px #2aff60;
    filter: brightness(1.2);
    transform: scale(1.05);
    opacity: 0.9;
  }
}

.flag-color-neon {
  animation: neonPulseGreen 3s ease-in-out infinite;
  will-change: transform, opacity, filter;
}

@keyframes waterFlicker {
  0% {
    transform: translateY(0px) scale(1);
    filter: brightness(1);
    opacity: 0.9;
    background-color: #00bfff;
  }
  12% {
    transform: translateY(-1px) scale(1.01);
    filter: brightness(1.05);
    opacity: 0.95;
    background-color: #1e90ff;
  }
  27% {
    transform: translateY(1px) scale(1.02);
    filter: brightness(1.15);
    opacity: 1;
    background-color: #87cefa;
  }
  45% {
    transform: translateY(-0.5px) scale(1.01);
    filter: brightness(1.05);
    opacity: 0.93;
    background-color: #4682b4;
  }
  63% {
    transform: translateY(0.7px) scale(1.03);
    filter: brightness(1.2);
    opacity: 1;
    background-color: #87cefa;
  }
  80% {
    transform: translateY(-1px) scale(1);
    filter: brightness(1);
    opacity: 0.88;
    background-color: #1e90ff;
  }
  100% {
    transform: translateY(0px) scale(1);
    filter: brightness(1);
    opacity: 0.9;
    background-color: #00bfff;
  }
}

.flag-color-water {
  animation: waterFlicker 6.2s ease-in-out infinite;
  will-change: transform, opacity, filter;
}

@keyframes lavaFlow {
  0% {
    background-color: #ff4500;
    filter: brightness(1.1);
    transform: scale(1);
  }
  20% {
    background-color: #ff6347;
    filter: brightness(1.2);
    transform: scale(1.02);
  }
  40% {
    background-color: #ff8c00;
    filter: brightness(1.3);
    transform: scale(1.03);
  }
  60% {
    background-color: #ff4500;
    filter: brightness(1.4);
    transform: scale(1.01);
  }
  80% {
    background-color: #ff0000;
    filter: brightness(1.2);
    transform: scale(1);
  }
  100% {
    background-color: #ff4500;
    filter: brightness(1.1);
    transform: scale(1);
  }
}

.flag-color-lava {
  animation: lavaFlow 6s ease-in-out infinite;
  will-change: background-color, filter, transform;
}
