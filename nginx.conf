# Map URI to ports
map $uri $port {
    ~^/w0/    3001;
    ~^/w1/    3002;
    ~^/w2/    3003;
    ~^/w3/    3004;
    ~^/w4/    3005;
    ~^/w5/    3006;
    ~^/w6/    3007;
    ~^/w7/    3008;
    ~^/w8/    3009;
    ~^/w9/    3010;
    ~^/w10/   3011;
    ~^/w11/   3012;
    ~^/w12/   3013;
    ~^/w13/   3014;
    ~^/w14/   3015;
    ~^/w15/   3016;
    ~^/w16/   3017;
    ~^/w17/   3018;
    ~^/w18/   3019;
    ~^/w19/   3020;
    ~^/w20/   3021;
    ~^/w21/   3022;
    ~^/w22/   3023;
    ~^/w23/   3024;
    ~^/w24/   3025;
    ~^/w25/   3026;
    ~^/w26/   3027;
    ~^/w27/   3028;
    ~^/w28/   3029;
    ~^/w29/   3030;
    ~^/w30/   3031;
    ~^/w31/   3032;
    ~^/w32/   3033;
    ~^/w33/   3034;
    ~^/w34/   3035;
    ~^/w35/   3036;
    ~^/w36/   3037;
    ~^/w37/   3038;
    ~^/w38/   3039;
    ~^/w39/   3040;
    ~^/w40/   3041;
    default   3000;
}

# WebSocket settings
map $http_upgrade $connection_upgrade {
    default upgrade;
    ''      close;
}

# WebSocket path handling
map $uri $uri_path {
    ~^/w\d+(/.*)?$    $1;
    default           $uri;
}

# Cache configuration
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=STATIC:10m inactive=24h max_size=1g;
# API cache for frequently requested endpoints
proxy_cache_path /var/cache/nginx/api levels=1:2 keys_zone=API_CACHE:10m inactive=60m max_size=100m;

server {
    listen 80 default_server;
    
    # Logging
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;
    
    # Static file handling with proper MIME types and consistent caching
    location ~* \.(jpg|jpeg|png|gif|ico|svg|webp|woff|woff2|ttf|eot)$ {
        proxy_pass http://127.0.0.1:3000;
        
        # Include MIME types
        include /etc/nginx/mime.types;
        
        # Cache configuration for static files
        proxy_cache STATIC;
        proxy_cache_valid 200 302 24h;  # Cache successful responses for 24 hours
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        proxy_cache_lock on;
        
        # Show cache status in response headers
        add_header X-Cache-Status $upstream_cache_status;
        
        # Standard proxy headers
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Default cache policy for static files
        add_header Cache-Control "public, max-age=86400";  # 24 hours
    }
    
    # /api/public_lobbies endpoint - Cache for 1 second to handle high request volume
    location = /api/public_lobbies {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        
        # Cache configuration
        proxy_cache API_CACHE;
        proxy_cache_valid 200 1s;
        proxy_cache_use_stale updating error timeout http_500 http_502 http_503 http_504;
        proxy_cache_lock on;
        add_header X-Cache-Status $upstream_cache_status;
        
        # Standard proxy headers
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # /api/env endpoint - Cache for 1 hour
    location = /api/env {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        
        # Cache configuration
        proxy_cache API_CACHE;
        proxy_cache_valid 200 1h;  # Cache successful responses for 1 hour
        proxy_cache_use_stale error timeout http_500 http_502 http_503 http_504;
        proxy_cache_lock on;
        add_header X-Cache-Status $upstream_cache_status;
        
        # Standard proxy headers
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Binary files caching
    location ~* \.(bin|dat|exe|dll|so|dylib)$ {
        proxy_pass http://127.0.0.1:3000;
        add_header Cache-Control "public, max-age=31536000, immutable";  # 1 year for binary files
        
        proxy_cache STATIC;
        proxy_cache_valid 200 302 24h;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        proxy_cache_lock on;
        add_header X-Cache-Status $upstream_cache_status;
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Specific file type caching rules (outside the /static/ location)
    location ~* \.js$ {
        proxy_pass http://127.0.0.1:3000;
        add_header Content-Type application/javascript;
        add_header Cache-Control "public, max-age=31536000, immutable";  # 1 year for JS files
        
        proxy_cache STATIC;
        proxy_cache_valid 200 302 24h;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        proxy_cache_lock on;
        add_header X-Cache-Status $upstream_cache_status;
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location ~* \.css$ {
        proxy_pass http://127.0.0.1:3000;
        add_header Content-Type text/css;
        add_header Cache-Control "public, max-age=31536000, immutable";  # 1 year for CSS files
        
        proxy_cache STATIC;
        proxy_cache_valid 200 302 24h;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        proxy_cache_lock on;
        add_header X-Cache-Status $upstream_cache_status;
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Updated HTML file caching - 1 second cache
    location ~* \.html$ {
        proxy_pass http://127.0.0.1:3000;
        add_header Content-Type text/html;
        add_header Cache-Control "public, max-age=1";  # 1 second for HTML files
        
        proxy_cache STATIC;
        proxy_cache_valid 200 302 1s;  # Cache successful responses for 1 second
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        proxy_cache_lock on;
        add_header X-Cache-Status $upstream_cache_status;
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Root location with short Nginx cache but no browser cache
    location = / {
        proxy_pass http://127.0.0.1:3000;
        
        # Tell browsers not to cache
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
        
        # But let Nginx cache for 1 second to reduce load
        proxy_cache STATIC;
        proxy_cache_valid 200 302 1s;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        proxy_cache_lock on;
        
        # Show cache status in response headers
        add_header X-Cache-Status $upstream_cache_status;
        
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Default location for all other requests
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Worker locations
    location ~* ^/w(\d+)(/.*)?$ {
        set $worker $1;
        set $worker_port 3001;
        
        if ($worker = "0") { set $worker_port 3001; }
        if ($worker = "1") { set $worker_port 3002; }
        if ($worker = "2") { set $worker_port 3003; }
        if ($worker = "3") { set $worker_port 3004; }
        if ($worker = "4") { set $worker_port 3005; }
        if ($worker = "5") { set $worker_port 3006; }
        if ($worker = "6") { set $worker_port 3007; }
        if ($worker = "7") { set $worker_port 3008; }
        if ($worker = "8") { set $worker_port 3009; }
        if ($worker = "9") { set $worker_port 3010; }
        if ($worker = "10") { set $worker_port 3011; }
        if ($worker = "11") { set $worker_port 3012; }
        if ($worker = "12") { set $worker_port 3013; }
        if ($worker = "13") { set $worker_port 3014; }
        if ($worker = "14") { set $worker_port 3015; }
        if ($worker = "15") { set $worker_port 3016; }
        if ($worker = "16") { set $worker_port 3017; }
        if ($worker = "17") { set $worker_port 3018; }
        if ($worker = "18") { set $worker_port 3019; }
        if ($worker = "19") { set $worker_port 3020; }
        if ($worker = "20") { set $worker_port 3021; }
        if ($worker = "21") { set $worker_port 3022; }
        if ($worker = "22") { set $worker_port 3023; }
        if ($worker = "23") { set $worker_port 3024; }
        if ($worker = "24") { set $worker_port 3025; }
        if ($worker = "25") { set $worker_port 3026; }
        if ($worker = "26") { set $worker_port 3027; }
        if ($worker = "27") { set $worker_port 3028; }
        if ($worker = "28") { set $worker_port 3029; }
        if ($worker = "29") { set $worker_port 3030; }
        if ($worker = "30") { set $worker_port 3031; }
        if ($worker = "31") { set $worker_port 3032; }
        if ($worker = "32") { set $worker_port 3033; }
        if ($worker = "33") { set $worker_port 3034; }
        if ($worker = "34") { set $worker_port 3035; }
        if ($worker = "35") { set $worker_port 3036; }
        if ($worker = "36") { set $worker_port 3037; }
        if ($worker = "37") { set $worker_port 3038; }
        if ($worker = "38") { set $worker_port 3039; }
        if ($worker = "39") { set $worker_port 3040; }
        if ($worker = "40") { set $worker_port 3041; }
        
        proxy_pass http://127.0.0.1:$worker_port$2;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}