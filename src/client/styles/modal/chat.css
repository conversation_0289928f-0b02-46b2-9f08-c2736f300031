/* .w. */

.chat-columns {
  display: flex;
  gap: 16px;
  padding: 12px;
  overflow-x: auto;
}

.chat-column {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 120px;
}

.column-title {
  font-weight: bold;
  margin-bottom: 4px;
}

.chat-option-button {
  background: #333;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  text-align: left;
  cursor: pointer;
}

.chat-option-button.selected {
  background-color: #66c;
}

.chat-preview {
  margin: 10px 12px;
  padding: 10px;
  background: #222;
  color: white;
  border-radius: 6px;
  text-align: center;
}

.chat-send {
  display: flex;
  justify-content: flex-end;
  padding: 0 12px 12px;
}

.chat-send-button {
  background: #4caf50;
  color: white;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.chat-send-button:disabled {
  background: #666;
  cursor: not-allowed;
}

.chat-column {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 140px;
}

.player-search-input {
  padding: 6px 8px;
  border-radius: 4px;
  border: 1px solid #666;
  font-size: 14px;
  outline: none;

  background-color: #fff;
  color: #000;
}

.player-scroll-area {
  max-height: 240px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding-right: 4px;
}

.phrase-scroll-area {
  max-height: 280px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding-right: 4px;
}
