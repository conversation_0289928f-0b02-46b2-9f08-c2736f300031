{"name": "openfront-client", "scripts": {"build-dev": "webpack --config webpack.config.js --mode development", "build-prod": "webpack --config webpack.config.js --mode production", "start:client": "webpack serve --open --node-env development", "start:server": "node --loader ts-node/esm --experimental-specifier-resolution=node src/server/Server.ts", "start:server-dev": "cross-env GAME_ENV=dev node --loader ts-node/esm --experimental-specifier-resolution=node src/server/Server.ts", "dev": "cross-env GAME_ENV=dev concurrently \"npm run start:client\" \"npm run start:server-dev\"", "tunnel": "npm run build-prod && npm run start:server", "test": "jest", "perf": "npx tsx tests/perf/*.ts", "test:coverage": "jest --coverage", "format": "prettier --ignore-unknown --write .", "lint": "eslint", "lint:fix": "eslint --fix", "prepare": "husky"}, "lint-staged": {"**/*": ["eslint --fix", "prettier --ignore-unknown --write"]}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/preset-typescript": "^7.24.7", "@datastructures-js/priority-queue": "^6.3.3", "@eslint/compat": "^1.2.7", "@eslint/js": "^9.21.0", "@types/benchmark": "^2.1.5", "@types/chai": "^4.3.17", "@types/d3": "^7.4.3", "@types/express": "^4.17.23", "@types/google-protobuf": "^3.15.12", "@types/hammerjs": "^2.0.46", "@types/jest": "^30.0.0", "@types/jquery": "^3.5.31", "@types/js-yaml": "^4.0.9", "@types/msgpack5": "^3.4.6", "@types/node": "^22.10.2", "@types/pg": "^8.11.11", "@types/sinon": "^17.0.3", "@types/systeminformation": "^3.23.1", "@types/ws": "^8.5.11", "autoprefixer": "^10.4.20", "benchmark": "^2.1.4", "binary-base64-loader": "^1.0.0", "binary-loader": "^0.0.1", "canvas": "^3.1.0", "chai": "^5.1.1", "concurrently": "^8.2.2", "copy-webpack-plugin": "^13.0.0", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "d3": "^7.9.0", "eslint": "^9.21.0", "eslint-config-prettier": "^10.1.1", "eslint-formatter-gha": "^1.5.2", "eslint-webpack-plugin": "^5.0.0", "file-loader": "^6.2.0", "globals": "^16.0.0", "html-inline-script-webpack-plugin": "^3.2.1", "html-loader": "^5.1.0", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "lint-staged": "^16.1.2", "lit": "^3.3.1", "lit-markdown": "^1.3.2", "mrmime": "^2.0.0", "pixi-filters": "^6.1.4", "pixi.js": "^8.11.0", "postcss": "^8.5.1", "postcss-loader": "^8.1.1", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-sh": "^0.17.4", "protobufjs": "^7.5.3", "raw-loader": "^4.0.2", "sinon": "^21.0.0", "sinon-chai": "^4.0.0", "style-loader": "^4.0.0", "tailwindcss": "^3.4.17", "ts-jest": "^29.2.4", "ts-loader": "^9.5.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.17.0", "typescript": "^5.7.2", "typescript-eslint": "^8.26.0", "webpack": "^5.100.2", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2", "worker-loader": "^3.0.8"}, "dependencies": {"@aws-sdk/client-s3": "^3.758.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/api-logs": "^0.200.0", "@opentelemetry/exporter-logs-otlp-http": "^0.200.0", "@opentelemetry/exporter-metrics-otlp-http": "^0.200.0", "@opentelemetry/resources": "^2.0.0", "@opentelemetry/sdk-logs": "^0.200.0", "@opentelemetry/sdk-metrics": "^2.0.0", "@opentelemetry/semantic-conventions": "^1.32.0", "@opentelemetry/winston-transport": "^0.11.0", "colord": "^2.9.3", "colorjs.io": "^0.5.2", "dompurify": "^3.1.7", "dotenv": "^16.5.0", "express": "^4.21.1", "express-rate-limit": "^7.5.0", "fastpriorityqueue": "^0.7.5", "ip-anonymize": "^0.1.0", "jose": "^6.0.10", "js-yaml": "^4.1.0", "nanoid": "^3.3.6", "obscenity": "^0.4.3", "ts-node": "^10.9.2", "uuid": "^11.1.0", "winston": "^3.17.0", "ws": "^8.18.0", "zod": "^4.0.5"}, "type": "module"}